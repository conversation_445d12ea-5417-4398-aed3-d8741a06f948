import { eq, desc, count, isNull, and } from 'drizzle-orm';
import { db, devices, deviceImmobilizerLogs, deviceScheduleJobLogs, admins, type NewDevice, type NewDeviceImmobilizerLog, type NewDeviceScheduleJobLog, type NewAdmin } from './index';

// Device functions
export async function getAllDevices() {
  const result = await db.select().from(devices).where(isNull(devices.deletedAt)).orderBy(desc(devices.createdAt));
  return result;
}

export async function getDeviceByImei(imei: string) {
  const result = await db.select().from(devices).where(and(eq(devices.imei, imei), isNull(devices.deletedAt))).limit(1);
  return result[0] || null;
}

export async function createDevice(device: NewDevice) {
  const result = await db.insert(devices).values(device).returning();
  return result[0];
}

export async function updateDeviceUserStatus(imei: string, userStatus: 'engineStop' | 'engineResume', taskId?: string) {
  const result = await db
    .update(devices)
    .set({
      userStatus: userStatus,
      latestTaskId: taskId,
      updatedAt: new Date()
    })
    .where(eq(devices.imei, imei))
    .returning();
  return result[0];
}

export async function updateDeviceSystemStatus(imei: string, systemStatus: 'engineStop' | 'engineResume') {
  const result = await db
    .update(devices)
    .set({
      systemStatus: systemStatus,
      updatedAt: new Date()
    })
    .where(eq(devices.imei, imei))
    .returning();
  return result[0];
}

export async function upsertDeviceUserStatus(imei: string, userStatus: 'engineStop' | 'engineResume', taskId?: string) {
  // Try to get existing device
  const existingDevice = await getDeviceByImei(imei);

  if (existingDevice) {
    // Update existing device user status
    return await updateDeviceUserStatus(imei, userStatus, taskId);
  } else {
    // Create new device
    return await createDevice({
      imei,
      userStatus: userStatus,
      latestTaskId: taskId,
    });
  }
}

export async function ensureDeviceExists(imei: string, defaultUserStatus: 'engineStop' | 'engineResume' = 'engineResume', defaultSystemStatus: 'engineStop' | 'engineResume' = 'engineResume') {
  // Check if device exists
  const existingDevice = await getDeviceByImei(imei);

  if (!existingDevice) {
    // Create device with default status
    return await createDevice({
      imei,
      userStatus: defaultUserStatus,
      systemStatus: defaultSystemStatus,
    });
  }

  return existingDevice;
}

export async function softDeleteDevice(imei: string) {
  const result = await db
    .update(devices)
    .set({
      deletedAt: new Date(),
      updatedAt: new Date()
    })
    .where(eq(devices.imei, imei))
    .returning();
  return result[0] || null;
}

export async function restoreDevice(imei: string) {
  const result = await db
    .update(devices)
    .set({
      deletedAt: null,
      updatedAt: new Date()
    })
    .where(eq(devices.imei, imei))
    .returning();
  return result[0] || null;
}

export async function getDeviceByImeiIncludingDeleted(imei: string) {
  const result = await db.select().from(devices).where(eq(devices.imei, imei)).limit(1);
  return result[0] || null;
}

export async function syncDeviceFromVehicles(imei: string, vehicleData: any) {
  // Check if device exists (including soft deleted ones)
  const existingDevice = await getDeviceByImeiIncludingDeleted(imei);

  // Determine system status from owl_mode
  const systemStatus = vehicleData.owl_mode === 'on' ? 'engineStop' : 'engineResume';

  const syncData = {
    name: vehicleData.vehicleno, // Vehicle name from pull_api_v2
    deviceId: vehicleData.deviceId?.toString(),
    systemStatus, // Set system status based on owl_mode
    status: vehicleData.motion ? 'moving' : 'stopped', // Derive status from motion
    latitude: vehicleData.latitude?.toString(),
    longitude: vehicleData.longitude?.toString(),
    speed: vehicleData.speed?.toString(),
    batteryLevel: vehicleData.battery?.toString(), // Battery level from pull_api_v2
    ignition: vehicleData.ignition?.toString(), // Ignition status (boolean)
    lastUpdate: vehicleData.lastUpdate, // Last update timestamp
    totalDistance: vehicleData.prevOdometer?.toString(), // Previous odometer as total distance
    dailyDistance: vehicleData.distance?.toString(), // Current distance
    vehicleType: vehicleData.type, // Vehicle type (bike, etc.)
    // Additional fields from pull_v2 API
    course: vehicleData.course?.toString(), // Course/direction
    motion: vehicleData.motion?.toString(), // Motion status (boolean)
    owlMode: vehicleData.owl_mode, // Owl mode status (on/off)
    bms: vehicleData.bms ? JSON.stringify(vehicleData.bms) : null, // BMS data as JSON string
    lastSyncAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null // Restore device if it was soft deleted
  };

  if (existingDevice) {
    // Update existing device with vehicle data and restore if deleted
    // Keep existing userStatus, only update systemStatus from API
    const result = await db
      .update(devices)
      .set(syncData)
      .where(eq(devices.imei, imei))
      .returning();
    return result[0];
  } else {
    // Create new device with vehicle data
    return await createDevice({
      imei,
      userStatus: 'engineResume', // Default user status for new devices from vehicles API
      ...syncData
    });
  }
}

// Immobilizer log functions
export async function getAllImmobilizerLogs(limit: number = 100) {
  const result = await db
    .select()
    .from(deviceImmobilizerLogs)
    .orderBy(desc(deviceImmobilizerLogs.createdAt))
    .limit(limit);
  return result;
}

export async function getAllImmobilizerLogsWithPagination(page: number = 1, pageSize: number = 20) {
  const offset = (page - 1) * pageSize;

  // Get total count
  const totalCountResult = await db
    .select({ count: count() })
    .from(deviceImmobilizerLogs);
  const totalCount = totalCountResult[0]?.count || 0;

  // Get paginated results
  const result = await db
    .select()
    .from(deviceImmobilizerLogs)
    .orderBy(desc(deviceImmobilizerLogs.createdAt))
    .limit(pageSize)
    .offset(offset);

  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    data: result,
    pagination: {
      page,
      pageSize,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
}

export async function createImmobilizerLog(log: NewDeviceImmobilizerLog) {
  const result = await db.insert(deviceImmobilizerLogs).values(log).returning();
  return result[0];
}

export async function createUserImmobilizerLog(
  imei: string,
  vehicleName: string | null,
  username: string,
  previousStatus: 'engineStop' | 'engineResume',
  newStatus: 'engineStop' | 'engineResume',
  taskId?: string | null,
  taskData?: any
) {
  // Ensure required fields are not empty
  if (!username || username.trim() === '') {
    throw new Error('Username is required for logging');
  }

  const result = await db.insert(deviceImmobilizerLogs).values({
    imei,
    vehicleName: vehicleName || 'Unknown Vehicle',
    username: username.trim(),
    previousStatus,
    newStatus,
    action: newStatus, // For backward compatibility
    taskId: taskId || null,
    taskData: taskData || null,
  }).returning();
  return result[0];
}

export async function getImmobilizerLogsByImei(imei: string, limit: number = 50) {
  const result = await db
    .select()
    .from(deviceImmobilizerLogs)
    .where(eq(deviceImmobilizerLogs.imei, imei))
    .orderBy(deviceImmobilizerLogs.createdAt)
    .limit(limit);
  return result;
}

export async function getLatestImmobilizerLog(imei: string) {
  const result = await db
    .select()
    .from(deviceImmobilizerLogs)
    .where(eq(deviceImmobilizerLogs.imei, imei))
    .orderBy(desc(deviceImmobilizerLogs.createdAt))
    .limit(1);
  return result[0] || null;
}

// Device schedule job log functions
export async function getAllScheduleJobLogs(limit: number = 100) {
  const result = await db
    .select()
    .from(deviceScheduleJobLogs)
    .orderBy(desc(deviceScheduleJobLogs.createdAt))
    .limit(limit);
  return result;
}

export async function getAllScheduleJobLogsWithPagination(page: number = 1, pageSize: number = 20) {
  const offset = (page - 1) * pageSize;

  // Get total count
  const totalCountResult = await db
    .select({ count: count() })
    .from(deviceScheduleJobLogs);
  const totalCount = totalCountResult[0]?.count || 0;

  // Get paginated results
  const result = await db
    .select()
    .from(deviceScheduleJobLogs)
    .orderBy(desc(deviceScheduleJobLogs.createdAt))
    .limit(pageSize)
    .offset(offset);

  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    data: result,
    pagination: {
      page,
      pageSize,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
}

export async function createScheduleJobLog(log: NewDeviceScheduleJobLog) {
  const result = await db.insert(deviceScheduleJobLogs).values(log).returning();
  return result[0];
}

export async function updateScheduleJobLogTaskData(id: number, taskData: any) {
  const result = await db
    .update(deviceScheduleJobLogs)
    .set({
      taskData: taskData,
      updatedAt: new Date()
    })
    .where(eq(deviceScheduleJobLogs.id, id))
    .returning();
  return result[0];
}

export async function getScheduleJobLogsByImei(imei: string, limit: number = 50) {
  const result = await db
    .select()
    .from(deviceScheduleJobLogs)
    .where(eq(deviceScheduleJobLogs.imei, imei))
    .orderBy(desc(deviceScheduleJobLogs.createdAt))
    .limit(limit);
  return result;
}

export async function getLatestScheduleJobLog(imei: string) {
  const result = await db
    .select()
    .from(deviceScheduleJobLogs)
    .where(eq(deviceScheduleJobLogs.imei, imei))
    .orderBy(desc(deviceScheduleJobLogs.createdAt))
    .limit(1);
  return result[0] || null;
}

// Admin functions
export async function getAdminByUsername(username: string) {
  const result = await db.select().from(admins).where(eq(admins.username, username)).limit(1);
  return result[0] || null;
}

export async function createAdmin(admin: NewAdmin) {
  const result = await db.insert(admins).values(admin).returning();
  return result[0];
}

export async function updateAdminSyncTime(username: string, syncTime: Date) {
  const result = await db
    .update(admins)
    .set({
      latestSyncAt: syncTime,
      updatedAt: new Date()
    })
    .where(eq(admins.username, username))
    .returning();
  return result[0];
}

export async function getAllAdmins() {
  const result = await db.select().from(admins);
  return result;
}

// Single admin config functions (only one admin allowed)
export async function getAdminConfig() {
  const result = await db.select().from(admins).limit(1);
  return result[0] || null;
}

export async function upsertAdminConfig(username: string, password: string) {
  // Check if admin config exists
  const existingAdmin = await getAdminConfig();

  if (existingAdmin) {
    // Update existing admin
    const result = await db
      .update(admins)
      .set({
        username,
        password,
        updatedAt: new Date()
      })
      .where(eq(admins.id, existingAdmin.id))
      .returning();
    return result[0];
  } else {
    // Create new admin
    const result = await db.insert(admins).values({
      username,
      password,
    }).returning();
    return result[0];
  }
}

// Update admin sync time for single admin config
export async function updateAdminConfigSyncTime() {
  const existingAdmin = await getAdminConfig();

  if (existingAdmin) {
    const result = await db
      .update(admins)
      .set({
        latestSyncAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(admins.id, existingAdmin.id))
      .returning();
    return result[0];
  }

  return null;
}

// Update device is_retry status
export async function updateDeviceRetryStatus(imei: string, isRetry: boolean) {
  const result = await db
    .update(devices)
    .set({
      isRetry: isRetry,
      updatedAt: new Date()
    })
    .where(eq(devices.imei, imei))
    .returning();
  return result[0];
}

// Check if device should be marked as no-retry based on 7 days of failed attempts
export async function checkAndUpdateDeviceRetryStatus(imei: string) {
  // Get device info
  const device = await getDeviceByImei(imei);
  if (!device || !device.isRetry) {
    return device; // Already marked as no-retry or device not found
  }

  // Check failed attempts in the last 7 days
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

  // Get schedule job logs for this device in the last 7 days
  const recentLogs = await db
    .select()
    .from(deviceScheduleJobLogs)
    .where(
      and(
        eq(deviceScheduleJobLogs.imei, imei),
        eq(deviceScheduleJobLogs.trigger, 'api'), // Only API/scheduled calls
        // Only logs from the last 7 days
        // Note: We'll check this in application logic since Drizzle date comparison can be tricky
      )
    )
    .orderBy(desc(deviceScheduleJobLogs.createdAt))
    .limit(50); // Get recent logs to analyze

  // Filter logs from last 7 days and check if all are failed due to device offline
  const recentFailedLogs = recentLogs.filter(log => {
    const logDate = new Date(log.createdAt);
    const isRecent = logDate >= sevenDaysAgo;
    const isFailed = log.success === 'false';
    const isDeviceOffline = log.errorMessage && (
      log.errorMessage.toLowerCase().includes('offline') ||
      log.errorMessage.toLowerCase().includes('device not responding') ||
      log.errorMessage.toLowerCase().includes('timeout') ||
      (log.taskData &&
       typeof log.taskData === 'object' &&
       log.taskData.data &&
       log.taskData.data.status === 'failed' &&
       log.taskData.data.message &&
       log.taskData.data.message.toLowerCase().includes('offline'))
    );

    return isRecent && isFailed && isDeviceOffline;
  });

  // Check if we have at least 3 failed attempts in the last 7 days due to device being offline
  if (recentFailedLogs.length >= 3) {
    // Check if there are any successful attempts in the last 7 days
    const recentSuccessLogs = recentLogs.filter(log => {
      const logDate = new Date(log.createdAt);
      const isRecent = logDate >= sevenDaysAgo;
      const isSuccess = log.success === 'true';
      return isRecent && isSuccess;
    });

    // If no successful attempts and multiple failed offline attempts, mark as no-retry
    if (recentSuccessLogs.length === 0) {
      console.log(`Device ${imei} marked as no-retry due to 7 days of offline failures`);
      return await updateDeviceRetryStatus(imei, false);
    }
  }

  return device;
}

// Reset device retry status to true (allow retries again)
export async function resetDeviceRetryStatus(imei: string) {
  const result = await db
    .update(devices)
    .set({
      isRetry: true,
      updatedAt: new Date()
    })
    .where(eq(devices.imei, imei))
    .returning();
  return result[0];
}


