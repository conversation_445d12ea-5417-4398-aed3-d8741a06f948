import { pgTable, serial, text, timestamp, pgEnum, jsonb, boolean, integer } from 'drizzle-orm/pg-core';

// Enum for immobilizer actions
export const immobilizerActionEnum = pgEnum('immobilizer_action', ['engineStop', 'engineResume']);

// Devices table
export const devices = pgTable('devices', {
  id: serial('id').primaryKey(),
  imei: text('imei').notNull().unique(),
  name: text('name'), // Vehicle name from Track360
  deviceId: text('device_id'), // Device ID from Track360
  userStatus: text('user_status').notNull().default('engineResume'), // Status set by user (engineStop/engineResume)
  systemStatus: text('system_status').notNull().default('engineResume'), // Status from Track360 API (engineStop/engineResume)
  latestTaskId: text('latest_task_id'), // Latest task ID from immobilizer API
  lastSyncAt: timestamp('last_sync_at'), // Last sync timestamp
  isRetry: boolean('is_retry').notNull().default(true), // Whether to retry set owl API calls (false after 7 days of failed attempts)
  // Vehicle data from Track360 API pull_v2
  status: text('status'), // Vehicle status from Track360
  latitude: text('latitude'), // GPS latitude
  longitude: text('longitude'), // GPS longitude
  speed: text('speed'), // Vehicle speed
  batteryLevel: text('battery_level'), // Battery level (from 'battery' field)
  ignition: text('ignition'), // Ignition status (boolean from API)
  lastUpdate: text('last_update'), // Last update from Track360
  totalDistance: text('total_distance'), // Total distance (from 'prevOdometer')
  dailyDistance: text('daily_distance'), // Daily distance (from 'distance')
  vehicleType: text('vehicle_type'), // Vehicle type (from 'type')
  // Additional fields from pull_v2 API
  course: text('course'), // Course/direction from API
  motion: text('motion'), // Motion status (boolean from API)
  owlMode: text('owl_mode'), // Owl mode status (on/off)
  bms: text('bms'), // BMS data (can be null)
  deletedAt: timestamp('deleted_at'), // Soft delete timestamp
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Device immobilizer logs table
export const deviceImmobilizerLogs = pgTable('device_immobilizer_logs', {
  id: serial('id').primaryKey(),
  imei: text('imei').notNull(),
  vehicleName: text('vehicle_name'), // Vehicle name for display
  username: text('username'), // User who made the change (nullable for migration)
  previousStatus: immobilizerActionEnum('previous_status'), // Previous status (nullable for migration)
  newStatus: immobilizerActionEnum('new_status'), // New status (nullable for migration)
  action: immobilizerActionEnum('action').notNull(), // For backward compatibility
  taskId: text('task_id'), // Task ID from immobilizer API response (optional)
  taskData: jsonb('task_data'), // Full API response data for task ID (optional)
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Device schedule job logs table
export const deviceScheduleJobLogs = pgTable('device_schedule_job_logs', {
  id: serial('id').primaryKey(),
  imei: text('imei').notNull(),
  action: immobilizerActionEnum('action').notNull(), // engineStop or engineResume
  trigger: text('trigger').notNull().default('api'), // 'api' for N8N/scheduled, 'user' for manual user action
  taskId: text('task_id'), // Task ID from immobilizer API response
  apiResponse: text('api_response'), // Full API response as JSON string from set owl mode
  taskData: jsonb('task_data'), // Response from get task status API
  success: text('success').notNull().default('pending'), // 'pending', 'true', or 'false' as string
  errorMessage: text('error_message'), // Error message if failed
  executedAt: timestamp('executed_at').defaultNow().notNull(), // When the job was executed
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Pull API v2 request logs table (keep only last 100 requests)
export const pullApiLogs = pgTable('pull_api_logs', {
  id: serial('id').primaryKey(),
  endpoint: text('endpoint').notNull(), // The API endpoint called
  method: text('method').notNull().default('GET'), // HTTP method
  requestHeaders: jsonb('request_headers'), // Request headers as JSON
  responseStatus: integer('response_status'), // HTTP response status
  responseHeaders: jsonb('response_headers'), // Response headers as JSON
  responseData: jsonb('response_data'), // Response data as JSON (truncated if too large)
  responseSize: integer('response_size'), // Size of response in bytes
  duration: integer('duration'), // Request duration in milliseconds
  success: boolean('success').notNull(), // Whether request was successful
  errorMessage: text('error_message'), // Error message if failed
  triggeredBy: text('triggered_by').notNull(), // What triggered this request (sync, schedule, manual)
  adminUsername: text('admin_username'), // Admin username who triggered the request
  executedAt: timestamp('executed_at').defaultNow().notNull(), // When the request was executed
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Admins table
export const admins = pgTable('admins', {
  id: serial('id').primaryKey(),
  username: text('username').notNull().unique(),
  password: text('password').notNull(),
  latestSyncAt: timestamp('latest_sync_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Types for TypeScript
export type Device = typeof devices.$inferSelect;
export type NewDevice = typeof devices.$inferInsert;
export type DeviceImmobilizerLog = typeof deviceImmobilizerLogs.$inferSelect;
export type NewDeviceImmobilizerLog = typeof deviceImmobilizerLogs.$inferInsert;
export type DeviceScheduleJobLog = typeof deviceScheduleJobLogs.$inferSelect;
export type NewDeviceScheduleJobLog = typeof deviceScheduleJobLogs.$inferInsert;
export type PullApiLog = typeof pullApiLogs.$inferSelect;
export type NewPullApiLog = typeof pullApiLogs.$inferInsert;

export type Admin = typeof admins.$inferSelect;
export type NewAdmin = typeof admins.$inferInsert;
