import { NextRequest, NextResponse } from 'next/server';
import { syncDeviceFromVehicles, getAdminConfig, updateAdminSyncTime, getAllDevices, softDeleteDevice } from '@/lib/db/queries';

export async function POST(request: NextRequest) {
  try {
    // Get admin credentials from database
    const adminConfig = await getAdminConfig();

    if (!adminConfig) {
      return NextResponse.json(
        {
          success: false,
          error: 'Admin configuration not found. Please configure admin credentials first.',
        },
        { status: 400 }
      );
    }

    const username = adminConfig.username;
    const password = adminConfig.password;

    // Get current devices from database before sync
    const currentDevices = await getAllDevices();
    const currentImeis = new Set(currentDevices.map(device => device.imei));

    // Fetch vehicles from Track360 API using pull_api_v2
    const credentials = Buffer.from(`${username}:${password}`).toString('base64');
    const vehiclesResponse = await fetch(
      'https://prod-s2.track360.net.in/api/v1/auth/pull_api_v2?bms=true',
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
        cache: 'no-store',
      }
    );

    if (!vehiclesResponse.ok) {
      throw new Error(`Track360 API responded with status: ${vehiclesResponse.status}`);
    }

    const vehiclesData = await vehiclesResponse.json();

    // Check if the response has the expected structure for pull_api_v2
    if (!vehiclesData.data || !Array.isArray(vehiclesData.data)) {
      throw new Error('Invalid response format from Track360 API pull_api_v2');
    }

    // Extract IMEIs from vehicles data and sync to database
    const syncResults = [];
    const errors = [];
    const apiImeis = new Set();

    // First, sync all devices from API
    for (const vehicle of vehiclesData.data) {
      try {
        if (vehicle.deviceImei) {
          apiImeis.add(vehicle.deviceImei);
          const syncedDevice = await syncDeviceFromVehicles(vehicle.deviceImei, vehicle);
          syncResults.push({
            imei: vehicle.deviceImei,
            deviceId: vehicle.deviceId,
            name: vehicle.vehicleno,
            synced: true,
            device: syncedDevice
          });
        }
      } catch (error) {
        console.error(`Error syncing device ${vehicle.deviceImei}:`, error);
        errors.push({
          imei: vehicle.deviceImei,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Soft delete devices that are not in API response
    const devicesToDelete = [];
    for (const imei of currentImeis) {
      if (!apiImeis.has(imei)) {
        try {
          await softDeleteDevice(imei);
          devicesToDelete.push(imei);
        } catch (error) {
          console.error(`Error soft deleting device ${imei}:`, error);
          errors.push({
            imei,
            error: `Failed to soft delete: ${error instanceof Error ? error.message : 'Unknown error'}`
          });
        }
      }
    }

    // Update admin sync time
    await updateAdminSyncTime(adminConfig.username, new Date());

    console.log(`=== Device Sync Results ===`);
    console.log(`Total vehicles from API: ${vehiclesData.data.length}`);
    console.log(`Successfully synced: ${syncResults.length}`);
    console.log(`Soft deleted devices: ${devicesToDelete.length}`);
    console.log(`Errors: ${errors.length}`);

    return NextResponse.json({
      success: true,
      data: {
        totalVehicles: vehiclesData.data.length,
        syncedDevices: syncResults.length,
        softDeletedDevices: devicesToDelete.length,
        errors: errors.length,
        syncResults,
        softDeletedImeis: devicesToDelete,
        errors: errors.length > 0 ? errors : undefined
      },
      message: `Successfully synced ${syncResults.length} devices and soft deleted ${devicesToDelete.length} devices from ${vehiclesData.data.length} vehicles`
    });

  } catch (error) {
    console.error('Error syncing devices:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to sync devices',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
