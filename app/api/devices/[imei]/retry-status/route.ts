import { NextRequest, NextResponse } from 'next/server';
import { getDeviceByImei, updateDeviceRetryStatus, resetDeviceRetryStatus } from '@/lib/db/queries';

// GET /api/devices/[imei]/retry-status - Get device retry status
export async function GET(
  request: NextRequest,
  { params }: { params: { imei: string } }
) {
  try {
    const { imei } = params;

    if (!imei) {
      return NextResponse.json(
        {
          success: false,
          error: 'Device IMEI is required',
        },
        { status: 400 }
      );
    }

    const device = await getDeviceByImei(imei);

    if (!device) {
      return NextResponse.json(
        {
          success: false,
          error: 'Device not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        imei: device.imei,
        name: device.name,
        isRetry: device.isRetry,
        userStatus: device.userStatus,
        systemStatus: device.systemStatus,
        lastSyncAt: device.lastSyncAt,
      },
      message: 'Device retry status retrieved successfully',
    });

  } catch (error) {
    console.error('Error getting device retry status:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get device retry status',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PUT /api/devices/[imei]/retry-status - Update device retry status
export async function PUT(
  request: NextRequest,
  { params }: { params: { imei: string } }
) {
  try {
    const { imei } = params;
    const body = await request.json();
    const { isRetry } = body;

    if (!imei) {
      return NextResponse.json(
        {
          success: false,
          error: 'Device IMEI is required',
        },
        { status: 400 }
      );
    }

    if (typeof isRetry !== 'boolean') {
      return NextResponse.json(
        {
          success: false,
          error: 'isRetry must be a boolean value',
        },
        { status: 400 }
      );
    }

    const device = await getDeviceByImei(imei);

    if (!device) {
      return NextResponse.json(
        {
          success: false,
          error: 'Device not found',
        },
        { status: 404 }
      );
    }

    const updatedDevice = await updateDeviceRetryStatus(imei, isRetry);

    return NextResponse.json({
      success: true,
      data: {
        imei: updatedDevice.imei,
        name: updatedDevice.name,
        isRetry: updatedDevice.isRetry,
        userStatus: updatedDevice.userStatus,
        systemStatus: updatedDevice.systemStatus,
        previousRetryStatus: device.isRetry,
      },
      message: `Device retry status updated to ${isRetry ? 'enabled' : 'disabled'}`,
    });

  } catch (error) {
    console.error('Error updating device retry status:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update device retry status',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/devices/[imei]/retry-status/reset - Reset device retry status to true
export async function POST(
  request: NextRequest,
  { params }: { params: { imei: string } }
) {
  try {
    const { imei } = params;

    if (!imei) {
      return NextResponse.json(
        {
          success: false,
          error: 'Device IMEI is required',
        },
        { status: 400 }
      );
    }

    const device = await getDeviceByImei(imei);

    if (!device) {
      return NextResponse.json(
        {
          success: false,
          error: 'Device not found',
        },
        { status: 404 }
      );
    }

    const updatedDevice = await resetDeviceRetryStatus(imei);

    return NextResponse.json({
      success: true,
      data: {
        imei: updatedDevice.imei,
        name: updatedDevice.name,
        isRetry: updatedDevice.isRetry,
        userStatus: updatedDevice.userStatus,
        systemStatus: updatedDevice.systemStatus,
        previousRetryStatus: device.isRetry,
      },
      message: 'Device retry status reset to enabled',
    });

  } catch (error) {
    console.error('Error resetting device retry status:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to reset device retry status',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
