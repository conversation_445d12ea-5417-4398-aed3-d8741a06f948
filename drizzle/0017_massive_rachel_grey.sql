CREATE TABLE "pull_api_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"endpoint" text NOT NULL,
	"method" text DEFAULT 'GET' NOT NULL,
	"request_headers" jsonb,
	"response_status" integer,
	"response_headers" jsonb,
	"response_data" jsonb,
	"response_size" integer,
	"duration" integer,
	"success" boolean NOT NULL,
	"error_message" text,
	"triggered_by" text NOT NULL,
	"admin_username" text,
	"executed_at" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
